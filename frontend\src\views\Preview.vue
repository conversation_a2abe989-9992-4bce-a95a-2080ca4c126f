<template>
  <div class="preview-container">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <h2>{{ formInfo.name }}</h2>
        <span class="form-description" v-if="formInfo.description">
          {{ formInfo.description }}
        </span>
      </div>
      <div class="toolbar-right">
        <button @click="toggleMode" class="mode-toggle">
          {{ isPreviewMode ? "编辑模式" : "预览模式" }}
        </button>
        <div class="zoom-controls">
          <button @click="zoomOut" :disabled="zoomLevel <= 0.5">-</button>
          <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
          <button @click="zoomIn" :disabled="zoomLevel >= 2">+</button>
        </div>
        <button @click="resetForm" class="reset-btn">重置表单</button>
        <button @click="closePreview" class="close-btn">关闭预览</button>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content">
      <div
        class="form-container"
        :style="{
          transform: `scale(${zoomLevel})`,
          transformOrigin: 'top center',
        }"
      >
        <form @submit.prevent="handleSubmit" class="preview-form">
          <!-- 表单标题和描述 -->
          <div class="form-header" v-if="formInfo.name || formInfo.description">
            <h1 class="form-title">{{ formInfo.name }}</h1>
            <p class="form-desc" v-if="formInfo.description">
              {{ formInfo.description }}
            </p>
          </div>

          <!-- 渲染表单元素 -->
          <div
            class="form-elements"
            :style="{
              width: getCanvasSize().width + 'px',
              height: getCanvasSize().height + 'px',
            }"
          >
            <div
              v-for="(element, index) in elements"
              :key="element.id || index"
              class="form-element"
              :style="getElementStyle(element)"
            >
              <!-- 根据组件类型渲染不同的表单控件 -->
              <component
                :is="element.type"
                v-bind="getFormProps(element)"
                :modelValue="
                  needsModelValue(element.type)
                    ? formData[element.id]
                    : undefined
                "
                @update:modelValue="
                  needsModelValue(element.type)
                    ? updateFormData(element.id, $event)
                    : undefined
                "
              />
            </div>
          </div>

          <!-- 表单操作按钮 -->
          <div class="form-actions" v-if="isPreviewMode">
            <button type="submit" class="submit-btn">提交表单</button>
            <button type="button" @click="resetForm" class="reset-btn">
              重置
            </button>
          </div>
        </form>

        <!-- 表单数据预览 -->
        <div class="form-data-preview" v-if="!isPreviewMode">
          <h3>表单数据</h3>
          <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";

// 响应式数据
const elements = ref([]);
const formInfo = ref({
  name: "表单预览",
  description: "",
});
const formData = reactive({});
const isPreviewMode = ref(true);
const zoomLevel = ref(1);

// 从 sessionStorage 加载预览数据
const loadPreviewData = () => {
  try {
    const previewDataStr = sessionStorage.getItem("previewData");
    if (previewDataStr) {
      const previewData = JSON.parse(previewDataStr);
      elements.value = previewData.elements || [];
      formInfo.value = previewData.formInfo || formInfo.value;

      // 初始化表单数据
      initFormData();
    } else {
      alert("没有找到预览数据，请从编辑器重新打开预览");
      closePreview();
    }
  } catch (error) {
    console.error("加载预览数据失败:", error);
    alert("加载预览数据失败");
    closePreview();
  }
};

// 初始化表单数据
const initFormData = () => {
  elements.value.forEach((element) => {
    if (element.id) {
      // 根据组件类型设置默认值
      switch (element.type) {
        case "ElInput":
          formData[element.id] = element.props?.defaultValue || "";
          break;
        case "ElButton":
          // 按钮不需要数据
          break;
        case "ElText":
          // 文本不需要数据
          break;
        default:
          formData[element.id] = "";
      }
    }
  });
};

// 计算画布尺寸
const getCanvasSize = () => {
  if (elements.value.length === 0) {
    return { width: 800, height: 600 };
  }

  let maxX = 0;
  let maxY = 0;

  elements.value.forEach((element) => {
    const x = (element.position?.x || 0) + (element.size?.width || 120);
    const y = (element.position?.y || 0) + (element.size?.height || 40);
    maxX = Math.max(maxX, x);
    maxY = Math.max(maxY, y);
  });

  // 添加一些边距
  return {
    width: Math.max(800, maxX + 50),
    height: Math.max(600, maxY + 50),
  };
};

// 获取元素样式
const getElementStyle = (element) => {
  // 预览模式和编辑模式都保持相对位置
  return {
    position: "absolute",
    left: `${element.position?.x || 0}px`,
    top: `${element.position?.y || 0}px`,
    width: `${element.size?.width || 120}px`,
    minHeight: `${element.size?.height || 40}px`,
  };
};

// 判断组件是否需要v-model
const needsModelValue = (componentType) => {
  return componentType === "ElInput";
};

// 获取表单组件属性
const getFormProps = (element) => {
  const props = { ...element.props };

  // 为预览模式添加特殊的样式类
  if (isPreviewMode.value) {
    switch (element.type) {
      case "ElInput":
        return {
          ...props,
          class: "preview-input",
        };
      case "ElButton":
        return {
          ...props,
          class: "preview-button",
        };
      case "ElText":
        return {
          ...props,
          class: "preview-text",
        };
      default:
        return props;
    }
  }

  return props;
};

// 更新表单数据
const updateFormData = (elementId, value) => {
  formData[elementId] = value;
};

// 切换模式
const toggleMode = () => {
  isPreviewMode.value = !isPreviewMode.value;
};

// 缩放功能
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1);
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1);
  }
};

// 重置表单
const resetForm = () => {
  if (confirm("确定要重置表单数据吗？")) {
    initFormData();
  }
};

// 处理表单提交
const handleSubmit = async () => {
  try {
    // 收集表单数据
    const submitData = {};
    elements.value.forEach((element) => {
      if (needsModelValue(element.type) && element.id) {
        submitData[element.id] = formData[element.id] || "";
      }
    });

    console.log("表单提交数据:", submitData);

    // 这里可以调用API提交数据
    // await submitFormData(submitData);

    alert("表单提交成功！\n数据：" + JSON.stringify(submitData, null, 2));
  } catch (error) {
    console.error("表单提交失败:", error);
    alert("表单提交失败: " + error.message);
  }
};

// 关闭预览
const closePreview = () => {
  window.close();
};

// 组件挂载时加载数据
onMounted(() => {
  loadPreviewData();
});
</script>

<style lang="less" scoped>
// 变量定义
@primary-color: #41b883;
@secondary-color: #35495e;
@success-color: #67c23a;
@danger-color: #ff4757;
@white: #fff;
@background-light: #fafbfc;
@border-color: #e1e8ed;
@text-color: #333;
@text-muted: #666;
@shadow-light: rgba(0, 0, 0, 0.1);

.preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: @background-light;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: @white;
  border-bottom: 1px solid @border-color;
  box-shadow: 0 2px 4px @shadow-light;

  .toolbar-left {
    h2 {
      margin: 0;
      color: @text-color;
      font-size: 20px;
    }

    .form-description {
      color: @text-muted;
      font-size: 14px;
      margin-left: 10px;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;

    button {
      padding: 8px 16px;
      border: 1px solid @border-color;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;

      &.mode-toggle {
        background-color: @primary-color;
        color: @white;
        border-color: @primary-color;

        &:hover {
          background-color: @secondary-color;
        }
      }

      &.reset-btn {
        background-color: @white;
        color: @text-color;

        &:hover {
          background-color: @background-light;
        }
      }

      &.close-btn {
        background-color: @danger-color;
        color: @white;
        border-color: @danger-color;

        &:hover {
          background-color: darken(@danger-color, 10%);
        }
      }
    }

    .zoom-controls {
      display: flex;
      align-items: center;
      gap: 5px;
      padding: 4px 8px;
      border: 1px solid @border-color;
      border-radius: 4px;
      background-color: @white;

      button {
        width: 24px;
        height: 24px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: bold;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .zoom-level {
        font-size: 12px;
        color: @text-muted;
        min-width: 40px;
        text-align: center;
      }
    }
  }
}

.preview-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-container {
  margin: 0 auto;
  background-color: @white;
  border-radius: 8px;
  box-shadow: 0 2px 8px @shadow-light;
  overflow: hidden;
  width: fit-content;
  min-width: 800px;
}

.preview-form {
  padding: 30px;
}

.form-header {
  margin-bottom: 30px;
  text-align: center;
  border-bottom: 1px solid @border-color;
  padding-bottom: 20px;

  .form-title {
    margin: 0 0 10px 0;
    color: @text-color;
    font-size: 28px;
    font-weight: 600;
  }

  .form-desc {
    margin: 0;
    color: @text-muted;
    font-size: 16px;
    line-height: 1.5;
  }
}

.form-elements {
  position: relative;
  min-height: 400px;
  border: 1px solid @border-color;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 20px;

  .form-element {
    // 所有元素都使用绝对定位保持相对位置
    position: relative;
  }
}

// 预览组件样式
.preview-input {
  width: 100%;
  padding: 12px;
  border: 1px solid @border-color;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }
}

.preview-button {
  padding: 10px 20px;
  background-color: @primary-color;
  color: @white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;

  &:hover {
    background-color: @secondary-color;
  }
}

.preview-text {
  color: @text-color;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px 0;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid @border-color;
  display: flex;
  gap: 15px;
  justify-content: center;

  .submit-btn {
    padding: 12px 30px;
    background-color: @success-color;
    color: @white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: background-color 0.2s;

    &:hover {
      background-color: darken(@success-color, 10%);
    }
  }

  .reset-btn {
    padding: 12px 30px;
    background-color: @white;
    color: @text-color;
    border: 1px solid @border-color;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s;

    &:hover {
      background-color: @background-light;
    }
  }
}

.form-data-preview {
  padding: 20px;
  background-color: @background-light;
  border-top: 1px solid @border-color;

  h3 {
    margin: 0 0 15px 0;
    color: @text-color;
    font-size: 16px;
  }

  pre {
    background-color: @white;
    border: 1px solid @border-color;
    border-radius: 4px;
    padding: 15px;
    margin: 0;
    font-size: 12px;
    color: @text-color;
    overflow-x: auto;
  }
}
</style>
